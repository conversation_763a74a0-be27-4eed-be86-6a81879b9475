import React, { useState } from 'react';

const LLMJudgeSQLApp = () => {
  const [userPrompt, setUserPrompt] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showPipeline, setShowPipeline] = useState(false);
  const [steps, setSteps] = useState({
    userInput: '',
    schema: '',
    sql: '',
    evaluation: ''
  });
  const [loadingSteps, setLoadingSteps] = useState({
    schema: false,
    sql: false,
    evaluation: false
  });

  // Mock database schema
  const mockSchema = `-- E-commerce Database Schema
CREATE TABLE customers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(50),
    stock_quantity INTEGER DEFAULT 0
);

CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES customers(id),
    total_amount DECIMAL(10,2),
    order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'pending'
);

CREATE TABLE order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id),
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL
);`;

  const generateMockSQL = (prompt) => {
    const promptLower = prompt.toLowerCase();
    
    if (promptLower.includes('customers') && promptLower.includes('purchase')) {
      return `SELECT DISTINCT c.id, c.name, c.email
FROM customers c
JOIN orders o ON c.id = o.customer_id
WHERE o.order_date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY c.name;`;
    } else if (promptLower.includes('total') && promptLower.includes('sales')) {
      return `SELECT 
    DATE_TRUNC('month', o.order_date) as month,
    SUM(o.total_amount) as total_sales
FROM orders o
WHERE o.status = 'completed'
GROUP BY DATE_TRUNC('month', o.order_date)
ORDER BY month DESC;`;
    } else if (promptLower.includes('product') && promptLower.includes('stock')) {
      return `SELECT p.name, p.stock_quantity, p.category
FROM products p
WHERE p.stock_quantity < 10
ORDER BY p.stock_quantity ASC;`;
    } else {
      return `SELECT c.name, COUNT(o.id) as order_count
FROM customers c
LEFT JOIN orders o ON c.id = o.customer_id
GROUP BY c.id, c.name
ORDER BY order_count DESC;`;
    }
  };

  const generateMockEvaluation = () => {
    const evaluations = [
      {
        score: 9,
        assessment: "Excellent query that correctly addresses the user's request",
        correctness: "The query logic is sound and will return accurate results",
        efficiency: "Well-optimized with appropriate joins and indexing considerations",
        suggestions: "Consider adding LIMIT clause for large datasets"
      },
      {
        score: 7,
        assessment: "Good query with minor room for improvement",
        correctness: "Query is functionally correct",
        efficiency: "Could benefit from better indexing strategy",
        suggestions: "Add WHERE clause to filter unnecessary rows"
      },
      {
        score: 5,
        assessment: "Query works but has some issues",
        correctness: "Logic is mostly correct but may miss edge cases",
        efficiency: "Potential performance issues with large datasets",
        suggestions: "Optimize joins and consider using EXISTS instead of JOIN"
      }
    ];
    
    return evaluations[Math.floor(Math.random() * evaluations.length)];
  };

  const startPipeline = async () => {
    if (!userPrompt.trim()) {
      alert('Please enter a query prompt');
      return;
    }
    
    if (isProcessing) return;
    
    setIsProcessing(true);
    setShowPipeline(true);
    
    // Step 1: Display user input
    setSteps(prev => ({ ...prev, userInput: userPrompt }));
    
    // Step 2: Schema retrieval
    setTimeout(() => {
      setLoadingSteps(prev => ({ ...prev, schema: true }));
      setTimeout(() => {
        setSteps(prev => ({ ...prev, schema: mockSchema }));
        setLoadingSteps(prev => ({ ...prev, schema: false }));
        
        // Step 3: Generate SQL
        setTimeout(() => {
          setLoadingSteps(prev => ({ ...prev, sql: true }));
          setTimeout(() => {
            const generatedSQL = generateMockSQL(userPrompt);
            setSteps(prev => ({ ...prev, sql: generatedSQL }));
            setLoadingSteps(prev => ({ ...prev, sql: false }));
            
            // Step 4: Evaluate
            setTimeout(() => {
              setLoadingSteps(prev => ({ ...prev, evaluation: true }));
              setTimeout(() => {
                const evaluation = generateMockEvaluation();
                setSteps(prev => ({ ...prev, evaluation }));
                setLoadingSteps(prev => ({ ...prev, evaluation: false }));
                setIsProcessing(false);
              }, 2500);
            }, 1500);
          }, 2000);
        }, 1500);
      }, 1000);
    }, 1000);
  };

  const resetPipeline = () => {
    if (isProcessing) {
      if (!window.confirm('A process is currently running. Are you sure you want to reset?')) {
        return;
      }
    }
    
    setUserPrompt('');
    setShowPipeline(false);
    setIsProcessing(false);
    setSteps({
      userInput: '',
      schema: '',
      sql: '',
      evaluation: ''
    });
    setLoadingSteps({
      schema: false,
      sql: false,
      evaluation: false
    });
  };

  const handleKeyDown = (e) => {
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      startPipeline();
    }
  };

  // Loading Spinner Component
  const LoadingSpinner = ({ text }) => (
    <div className="flex items-center gap-3 text-indigo-600">
      <div className="w-5 h-5 border-2 border-gray-300 border-t-indigo-600 rounded-full animate-spin"></div>
      <span className="italic font-medium">{text}</span>
    </div>
  );

  // Code Block Component
  const CodeBlock = ({ children, isDark = false, className = "" }) => (
    <div className={`
      ${isDark 
        ? 'bg-gray-900 text-green-400 border-gray-700' 
        : 'bg-gray-50 text-gray-800 border-gray-200'
      } 
      border rounded-xl p-4 font-mono text-sm whitespace-pre-wrap overflow-x-auto
      shadow-inner ${className}
    `}>
      {children}
    </div>
  );

  // Evaluation Result Component
  const EvaluationResult = ({ evaluation }) => {
    const isPositive = evaluation.score >= 7;
    const gradientClass = isPositive 
      ? 'bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-500' 
      : 'bg-gradient-to-br from-red-500 via-pink-500 to-orange-500';
    
    return (
      <div className={`${gradientClass} text-white p-6 rounded-xl shadow-lg transform transition-all duration-300 hover:scale-105`}>
        <div className="backdrop-blur-sm bg-white/10 rounded-lg p-4">
          <h3 className="text-xl font-bold mb-4 flex items-center gap-2">
            <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
              {isPositive ? '✓' : '⚠'}
            </div>
            Evaluation Results
          </h3>
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="font-semibold min-w-0 flex-shrink-0">Score:</span>
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold">{evaluation.score}/10</span>
                <div className="flex-1 bg-white/20 rounded-full h-2">
                  <div 
                    className="bg-white rounded-full h-2 transition-all duration-500"
                    style={{ width: `${evaluation.score * 10}%` }}
                  ></div>
                </div>
              </div>
            </div>
            <div>
              <span className="font-semibold">Assessment:</span>
              <p className="mt-1 leading-relaxed">{evaluation.assessment}</p>
            </div>
            <div>
              <span className="font-semibold">Correctness:</span>
              <p className="mt-1 leading-relaxed">{evaluation.correctness}</p>
            </div>
            <div>
              <span className="font-semibold">Efficiency:</span>
              <p className="mt-1 leading-relaxed">{evaluation.efficiency}</p>
            </div>
            <div>
              <span className="font-semibold">Suggestions:</span>
              <p className="mt-1 leading-relaxed">{evaluation.suggestions}</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Pipeline Step Component
  const PipelineStep = ({ stepNumber, title, children, isActive = false }) => (
    <div className={`
      bg-white rounded-xl p-6 shadow-lg border-l-4 
      ${isActive ? 'border-indigo-500 shadow-xl' : 'border-gray-300'}
      transform transition-all duration-300 hover:translate-x-2 hover:shadow-xl
      backdrop-blur-sm
    `}>
      <div className="flex items-center mb-4">
        <div className={`
          w-12 h-12 rounded-full flex items-center justify-center font-bold text-white mr-4 shadow-lg
          ${isActive 
            ? 'bg-gradient-to-br from-indigo-500 to-purple-600' 
            : 'bg-gradient-to-br from-gray-400 to-gray-500'
          }
        `}>
          {stepNumber}
        </div>
        <h3 className="text-xl font-semibold text-gray-800 flex-1">{title}</h3>
        {isActive && (
          <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
        )}
      </div>
      <div className="ml-16">
        {children}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-800 p-5 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
      
      <div className="max-w-6xl mx-auto relative z-10">
        {/* Main Container */}
        <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl overflow-hidden border border-white/20">
          {/* Header */}
          <div className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white p-8 relative overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent"></div>
            <div className="relative z-10 text-center">
              <h1 className="text-4xl md:text-5xl font-light mb-3 tracking-wide">
                LLM Judge SQL Query Evaluator
              </h1>
              <p className="text-lg md:text-xl opacity-90 font-light">
                Generate SQL queries from natural language and evaluate them with AI
              </p>
              <div className="mt-4 flex justify-center gap-2">
                <div className="w-2 h-2 bg-white/60 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-white/60 rounded-full animate-bounce delay-100"></div>
                <div className="w-2 h-2 bg-white/60 rounded-full animate-bounce delay-200"></div>
              </div>
            </div>
          </div>
          
          {/* Main Content */}
          <div className="p-8 md:p-10">
            {/* Input Section */}
            <div className="mb-10">
              <label className="block font-semibold text-gray-700 mb-4 text-lg flex items-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-md flex items-center justify-center text-white text-xs font-bold">
                  ?
                </div>
                Enter your query prompt:
              </label>
              <div className="relative">
                <textarea
                  value={userPrompt}
                  onChange={(e) => setUserPrompt(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="e.g., Show me all customers who have made purchases in the last 30 days"
                  className="w-full p-5 border-2 border-gray-200 rounded-xl text-base resize-vertical min-h-32 transition-all duration-300 focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 shadow-sm hover:shadow-md font-medium placeholder-gray-400"
                  disabled={isProcessing}
                />
                {userPrompt && (
                  <div className="absolute top-2 right-2 text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded">
                    {userPrompt.length} chars
                  </div>
                )}
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 mt-6">
                <button
                  onClick={startPipeline}
                  disabled={isProcessing || !userPrompt.trim()}
                  className="flex-1 sm:flex-none px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl font-semibold text-base uppercase tracking-wide transition-all duration-300 hover:from-indigo-700 hover:to-purple-700 hover:transform hover:-translate-y-1 hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:hover:shadow-none flex items-center justify-center gap-2 shadow-lg"
                >
                  {isProcessing ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                      Processing...
                    </>
                  ) : (
                    <>
                      <span>🚀</span>
                      Process Query
                    </>
                  )}
                </button>
                <button
                  onClick={resetPipeline}
                  className="px-8 py-4 bg-white text-gray-600 border-2 border-gray-200 rounded-xl font-semibold text-base uppercase tracking-wide transition-all duration-300 hover:bg-gray-50 hover:border-gray-300 hover:transform hover:-translate-y-0.5 shadow-sm hover:shadow-md flex items-center justify-center gap-2"
                >
                  <span>🔄</span>
                  Reset
                </button>
              </div>
              
              {/* Keyboard shortcut hint */}
              <p className="text-sm text-gray-500 mt-2 text-center">
                💡 Tip: Press <kbd className="px-2 py-1 bg-gray-100 rounded text-xs font-mono">Ctrl+Enter</kbd> to process
              </p>
            </div>
            
            {/* Pipeline Steps */}
            {showPipeline && (
              <div className="space-y-6 animate-fadeIn">
                {/* Step 1: User Input */}
                <PipelineStep 
                  stepNumber="1" 
                  title="User Input" 
                  isActive={!!steps.userInput}
                >
                  {steps.userInput && (
                    <div className="space-y-3">
                      <p className="text-gray-600 font-medium">Original Query Prompt:</p>
                      <CodeBlock className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                        {steps.userInput}
                      </CodeBlock>
                    </div>
                  )}
                </PipelineStep>
                
                {/* Step 2: Schema */}
                <PipelineStep 
                  stepNumber="2" 
                  title="Database Schema Retrieval" 
                  isActive={loadingSteps.schema || !!steps.schema}
                >
                  {loadingSteps.schema ? (
                    <LoadingSpinner text="Fetching PostgreSQL database schema..." />
                  ) : steps.schema ? (
                    <div className="space-y-3">
                      <p className="text-gray-600 font-medium flex items-center gap-2">
                        <span>🗄️</span>
                        Database Schema Retrieved:
                      </p>
                      <CodeBlock>
                        {steps.schema}
                      </CodeBlock>
                    </div>
                  ) : null}
                </PipelineStep>
                
                {/* Step 3: SQL Generation */}
                <PipelineStep 
                  stepNumber="3" 
                  title="SQL Query Generation (Performer LLM)" 
                  isActive={loadingSteps.sql || !!steps.sql}
                >
                  {loadingSteps.sql ? (
                    <LoadingSpinner text="Performer LLM is generating SQL query..." />
                  ) : steps.sql ? (
                    <div className="space-y-3">
                      <p className="text-gray-600 font-medium flex items-center gap-2">
                        <span>⚡</span>
                        Generated SQL Query:
                      </p>
                      <CodeBlock isDark={true}>
                        {steps.sql}
                      </CodeBlock>
                    </div>
                  ) : null}
                </PipelineStep>
                
                {/* Step 4: Evaluation */}
                <PipelineStep 
                  stepNumber="4" 
                  title="Query Evaluation (Judge LLM)" 
                  isActive={loadingSteps.evaluation || !!steps.evaluation}
                >
                  {loadingSteps.evaluation ? (
                    <LoadingSpinner text="Judge LLM is evaluating the generated query..." />
                  ) : steps.evaluation ? (
                    <div className="space-y-3">
                      <p className="text-gray-600 font-medium flex items-center gap-2">
                        <span>⚖️</span>
                        Evaluation Complete:
                      </p>
                      <EvaluationResult evaluation={steps.evaluation} />
                    </div>
                  ) : null}
                </PipelineStep>
              </div>
            )}
          </div>
        </div>
      </div>
      
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.5s ease-out;
        }
      `}</style>
    </div>
  );
};

export default LLMJudgeSQLApp;